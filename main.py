from langchain_community.llms import LlamaCpp
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import Runnable
import streamlit as st
from dotenv import load_dotenv

load_dotenv(".env")

local_path = "models/DeepSeek-Coder-V2-Lite-Instruct-Q4_K_M.gguf"

@st.cache_resource
def load_llm():
    return LlamaCpp(
        model_path=local_path,
        n_ctx=2048,
        n_batch=32,
        n_threads=4,
        temperature=0,
        top_p=0.9,
        max_tokens=512,
        verbose=True,
    )

llm = load_llm()

st.title("Local Chatbot")
st.caption("Powered by LangChain + Streamlit")

prompt = ChatPromptTemplate.from_messages([
    ("system", "You are a professional code reviewer. You can analyze code from any language (Python, JavaScript, Java, etc.) and provide structured feedback."),
    ("user", '''  
First:
- Detect the programming language.

Then please review the following code and:
- Analyze it line-by-line. For each line with a potential issue or improvement, give a comment next to that line.
- Identify issues, code smells, or bad practices. Check for syntax correctness based on that language.
- Check if variable/method/class names are semantically meaningful and properly spelled (in English)
- Suggest improvements or better alternatives.
- Mention if the code follows best practices.
- Use the STAR method (Situation, Task, Action, Result) to explain any suggestion.
- If possible, provide a corrected or improved version of the problematic code segment.

Respond in short, clear English using this format:
- ❌ [Issue] Description
- ✅ [Suggestion] Description (with STAR reasoning)
- ⚡ [Improved Line] (if applicable)

Only mention lines with actual suggestions. Skip clean lines.

Code:
```{code}```''')
])

example_code = """
public class UserService {

    private DatabaseConnection db;

    public UserService() {
        db = new DatabaseConnection();
    }

    public void createUser(String username, String password) {
        if (username != null && password != null) {
            String query = "INSERT INTO users (username, password) VALUES ('" + username + "', '" + password + "')";
            db.execute(query);
        } else {
            console.log("Invalid input");
        }
    }

    public boolean authenticate(String username, String password) {
        String query = "SELECT * FROM users WHERE username = '" + username + "' AND password = '" + password + "'";
        ResultSet rs = db.query(query);
        try {
            print("Query result: " + rs.next());
            return rs.next();
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
}
"""

# chain: Runnable = prompt | llm
chain: Runnable = llm

# response = chain.invoke({"code": example_code})
response = chain.invoke("Hello, world!")
print("\n🤖 Bot Line-wise Review:\n", response)